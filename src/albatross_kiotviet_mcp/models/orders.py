"""Order models for KiotViet API."""

from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, Field
from .base import BaseResponse


class OrderDetail(BaseModel):
    """Mô hình dữ liệu chi tiết đơn đặt hàng."""
    productId: int = Field(description="ID sản phẩm")
    productCode: str = Field(description="Mã sản phẩm")
    productName: str = Field(description="Tên sản phẩm")
    quantity: float = Field(description="Số lượng")
    price: float = Field(description="Giá")
    discount: Optional[float] = None
    discountRatio: Optional[float] = None
    note: Optional[str] = None
    viewDiscount: Optional[float] = None


class OrderPayment(BaseModel):
    """Mô hình dữ liệu thanh toán đơn đặt hàng."""
    id: int = Field(description="ID thanh toán")
    code: str = Field(description="Mã thanh toán")
    amount: float = Field(description="Số tiền")
    accountId: Optional[int] = None
    bankAccount: Optional[str] = None
    description: Optional[str] = None
    method: Optional[str] = None
    status: Optional[int] = None
    statusValue: Optional[str] = None
    transDate: Optional[datetime] = None


class Order(BaseModel):
    """Mô hình dữ liệu đơn đặt hàng riêng lẻ theo API response."""
    id: int = Field(description="ID đơn đặt hàng")
    code: str = Field(description="Mã đơn đặt hàng")
    purchaseDate: Optional[datetime] = None
    branchId: Optional[int] = None
    branchName: Optional[str] = None
    soldById: Optional[int] = None
    soldByName: Optional[str] = None
    customerId: Optional[int] = None
    customerCode: Optional[str] = None
    customerName: Optional[str] = None
    total: Optional[float] = None
    totalPayment: Optional[float] = None
    status: Optional[int] = None
    statusValue: Optional[str] = None
    retailerId: Optional[int] = None
    description: Optional[str] = None
    usingCod: Optional[bool] = None
    modifiedDate: Optional[datetime] = None
    createdDate: Optional[datetime] = None
    SaleChannelId: Optional[int] = None
    PriceBookId: Optional[int] = None
    Extra: Optional[str] = None
    
    # Chi tiết đơn hàng
    orderDetails: Optional[List[OrderDetail]] = None
    
    # Thông tin thanh toán (chỉ có khi includePayment=True)
    payments: Optional[List[OrderPayment]] = None
    
    # Tên kênh bán hàng
    SaleChannelName: Optional[str] = None


class OrderResponse(BaseResponse):
    """Mô hình phản hồi cho endpoint API đơn đặt hàng."""
    data: List[Order] = Field(description="Dữ liệu đơn đặt hàng")