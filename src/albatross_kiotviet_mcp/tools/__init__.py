"""Tools package with improved architecture."""

# Import all tool implementations
from .get_categories_tool_impl import CategoriesToolImpl
from .get_branches_tool_impl import BranchesToolImpl
from .get_inventory_tool_impl import InventoryToolImpl
from .get_invoices_tool_impl import InvoicesToolImpl
from .calculate_daily_revenue_tool_impl import DailyRevenueToolImpl
from .get_product_by_code_tool_impl import ProductByCodeToolImpl
from .get_customers_tool_impl import CustomersToolImpl
from .get_purchase_orders_tool_impl import PurchaseOrdersToolImpl
from .get_returns_tool_impl import ReturnsToolImpl
from .get_cashflow_tool_impl import CashFlowToolImpl

# Import base classes and utilities
from .core import BaseTool, with_logging, with_validation, with_error_handling
from .schemas import (
    PaginationParams,
    DateRangeParams,
    CategoriesParams,
    BranchesParams,
    InventoryParams,
    InvoicesParams,
    RevenueParams,
    ProductByCodeParams,
    CustomersParams,
    PurchaseOrdersParams,
    ReturnsParams,
    CashFlowParams,
    OrdersParams,
)

# Import registration functions
from .register_tools import register_all_tools

__all__ = [
    # Tool implementations
    'CategoriesToolImpl',
    'BranchesToolImpl', 
    'InventoryToolImpl',
    'InvoicesToolImpl',
    'DailyRevenueToolImpl',
    'ProductByCodeToolImpl',
    'CustomersToolImpl',
    'PurchaseOrdersToolImpl',
    'ReturnsToolImpl',
    'CashFlowToolImpl',
    'OrdersToolImpl',
    
    # Base classes
    'BaseTool',
    
    # Schemas
    'PaginationParams',
    'DateRangeParams',
    'CategoriesParams',
    'BranchesParams',
    'InventoryParams',
    'InvoicesParams',
    'RevenueParams',
    'ProductByCodeParams',
    'CustomersParams',
    'PurchaseOrdersParams',
    'ReturnsParams',
    'CashFlowParams',
    'OrdersParams',
    
    # Middleware
    'with_logging',
    'with_validation', 
    'with_error_handling',
    
    # Registration functions
    'register_all_tools',
]