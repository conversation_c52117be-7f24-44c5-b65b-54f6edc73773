"""Implementation of get_orders tool for KiotViet MCP server."""

from typing import Dict, Any, Optional, List
from .core.base_tool import BaseTool
from .core.middleware import with_logging, with_validation, with_error_handling


class OrdersToolImpl(BaseTool):
    """Tool implementation for retrieving orders from KiotViet API."""
    
    async def execute(
        self,
        page_size: int = 50,
        current_item: int = 0,
        order_direction: str = "Asc",
        branch_ids: Optional[List[int]] = None,
        customer_ids: Optional[List[int]] = None,
        customer_code: Optional[str] = None,
        status: Optional[List[int]] = None,
        sale_channel_id: Optional[int] = None,
        include_payment: Optional[bool] = None,
        include_order_delivery: Optional[bool] = None,
        last_modified_from: Optional[str] = None,
        to_date: Optional[str] = None,
        created_date: Optional[str] = None,
        order_by: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        <PERSON><PERSON><PERSON> danh sách đơn đặt hàng từ KiotViet API với các tùy chọn lọc toàn diện.
        
        Args:
            page_size: Số lượng items trên mỗi trang (mặc định 50, tối đa 100)
            current_item: Vị trí bắt đầu lấy dữ liệu (mặc định 0)
            order_direction: Hướng sắp xếp - "Asc" hoặc "Desc"
            branch_ids: Danh sách ID chi nhánh để lọc
            customer_ids: Danh sách ID khách hàng để lọc
            customer_code: Mã khách hàng để tìm kiếm
            status: Danh sách trạng thái đơn hàng (1: Chờ xử lý, 2: Đang xử lý, 3: Hoàn thành, 4: Hủy)
            sale_channel_id: ID kênh bán hàng
            include_payment: Có bao gồm thông tin thanh toán
            include_order_delivery: Có bao gồm thông tin giao hàng
            last_modified_from: Thời gian cập nhật từ (YYYY-MM-DD hoặc YYYY-MM-DD HH:MM:SS)
            to_date: Thời gian cập nhật đến (YYYY-MM-DD hoặc YYYY-MM-DD HH:MM:SS)
            created_date: Ngày tạo đơn hàng (YYYY-MM-DD)
            order_by: Trường sắp xếp (id, code, purchaseDate, total, createdDate)
            
        Returns:
            Dict chứa danh sách đơn đặt hàng và metadata
            
        Raises:
            ValueError: Khi tham số không hợp lệ
            RuntimeError: Khi có lỗi từ API
        """
        
        # Validate parameters using base class methods
        self.validate_pagination(page_size, current_item)
        self.validate_order_direction(order_direction)
        
        # Validate status values if provided
        if status:
            valid_statuses = [1, 2, 3, 4]  # Chờ xử lý, Đang xử lý, Hoàn thành, Hủy
            for s in status:
                if s not in valid_statuses:
                    raise ValueError(f"Invalid status: {s}. Valid values are: {valid_statuses}")
        
        # Validate IDs are positive if provided
        if branch_ids:
            for branch_id in branch_ids:
                if branch_id <= 0:
                    raise ValueError(f"Branch ID must be positive: {branch_id}")
                    
        if customer_ids:
            for customer_id in customer_ids:
                if customer_id <= 0:
                    raise ValueError(f"Customer ID must be positive: {customer_id}")
        
        if sale_channel_id is not None and sale_channel_id <= 0:
            raise ValueError("Sale channel ID must be positive")
        
        # Execute API call
        result = await self.api_client.get_orders(
            page_size=page_size,
            current_item=current_item,
            order_direction=order_direction,
            branch_ids=branch_ids,
            customer_ids=customer_ids,
            customer_code=customer_code,
            status=status,
            sale_channel_id=sale_channel_id,
            include_payment=include_payment,
            include_order_delivery=include_order_delivery,
            last_modified_from=last_modified_from,
            to_date=to_date,
            created_date=created_date,
            order_by=order_by
        )
        
        return result.model_dump()