"""Tool registration using improved architecture."""

from typing import Annotated, Dict, Any, Optional, List, Union
from datetime import datetime
from fastmcp import FastMCP
from pydantic import Field

from albatross_kiotviet_mcp.tools.schemas.orders_schema import OrdersParams

from ..config import get_api_client
from .get_categories_tool_impl import CategoriesToolImpl
from .get_branches_tool_impl import BranchesToolImpl
from .get_inventory_tool_impl import InventoryToolImpl
from .get_invoices_tool_impl import InvoicesToolImpl
from .calculate_daily_revenue_tool_impl import DailyRevenueToolImpl
from .get_product_by_code_tool_impl import ProductByCodeToolImpl
from .get_customers_tool_impl import CustomersToolImpl
from .get_purchase_orders_tool_impl import PurchaseOrdersToolImpl
from .get_returns_tool_impl import ReturnsToolImpl
from .get_cashflow_tool_impl import CashFlowToolImpl
from .get_orders_tool_impl import OrdersToolImpl
from .schemas import CategoriesParams, BranchesParams, InventoryParams, InvoicesParams, RevenueParams, ProductByCodeParams, CustomersParams, PurchaseOrdersParams, ReturnsParams, CashFlowParams
from .core.middleware import with_logging, with_validation, with_error_handling


def register_all_tools(mcp: FastMCP) -> None:
    """Register all tools using improved architecture.

    Args:
        mcp: FastMCP server instance to register tools with
    """
    
    @mcp.tool()
    @with_logging
    @with_error_handling
    @with_validation(CategoriesParams)
    async def get_categories(
        page_size: Annotated[int, Field(description="Number of items per page (1-100)", ge=1, le=100)] = 50,
        current_item: Annotated[int, Field(description="Starting item index for pagination", ge=0)] = 0,
        order_direction: Annotated[str, Field(description="Sort order: Asc or Desc")] = "Asc",
        hierarchical_data: Annotated[bool, Field(description="Return hierarchical structure")] = False
    ) -> Dict[str, Any]:
        """Get product categories from KiotViet API with pagination support.
        
        This tool retrieves product categories from the KiotViet API with support for pagination,
        sorting, and hierarchical data structure options.
        
        Args:
            page_size: Number of items per page (default: 50, max: 100)
            current_item: Starting item index for pagination (default: 0)
            order_direction: Sort order - "Asc" or "Desc" (default: "Asc")
            hierarchical_data: Whether to return hierarchical structure (default: False)
        
        Returns:
            Dictionary containing category data from the API
        """
        api_client = get_api_client()
        async with api_client:
            tool = CategoriesToolImpl(api_client)
            return await tool.execute(
                page_size=page_size,
                current_item=current_item,
                order_direction=order_direction,
                hierarchical_data=hierarchical_data
            )

    @mcp.tool()
    @with_logging
    @with_error_handling
    @with_validation(BranchesParams)
    async def get_branches(
        page_size: Annotated[int, Field(description="Number of items per page (1-100)", ge=1, le=100)] = 50,
        current_item: Annotated[int, Field(description="Starting item index for pagination", ge=0)] = 0,
        order_direction: Annotated[str, Field(description="Sort order: Asc or Desc")] = "Asc"
    ) -> Dict[str, Any]:
        """Get branches from KiotViet API with pagination support.

        This tool retrieves branches from the KiotViet API with support for pagination
        and sorting.

        Args:
            page_size: Number of items per page (default: 50, max: 100)
            current_item: Starting item index for pagination (default: 0)
            order_direction: Sort order - "Asc" or "Desc" (default: "Asc")

        Returns:
            Dictionary containing branch data from the API
        """
        api_client = get_api_client()
        async with api_client:
            tool = BranchesToolImpl(api_client)
            return await tool.execute(
                page_size=page_size,
                current_item=current_item,
                order_direction=order_direction
            )

    @mcp.tool()
    @with_logging
    @with_error_handling
    @with_validation(InventoryParams)
    async def get_inventory(
        page_size: Annotated[int, Field(description="Number of items per page (1-100)", ge=1, le=100)] = 20,
        current_item: Annotated[int, Field(description="Starting item index for pagination", ge=0)] = 0,
        order_by: Annotated[Optional[str], Field(description="Sort data by field (e.g., 'Code')")] = None,
        last_modified_from: Annotated[Optional[Union[str, datetime]], Field(description="Filter by last modified time (ISO format)")] = None,
        branch_ids: Annotated[Optional[List[int]], Field(description="List of branch IDs to filter by")] = None
    ) -> Dict[str, Any]:
        """Get product inventory/stock data from KiotViet API with pagination and filtering support.

        This tool retrieves product inventory data from the KiotViet API, showing available
        and reserved stock quantities for each product across different branches.

        Args:
            page_size: Number of items per page (default: 20, max: 100)
            current_item: Starting item index for pagination (default: 0)
            order_by: Sort data by field (e.g., "Code") (optional)
            last_modified_from: Filter by last modified time (ISO datetime string) (optional)
            branch_ids: List of branch IDs to filter by (optional)

        Returns:
            Dictionary containing inventory data with product details and stock levels
        """
        api_client = get_api_client()
        async with api_client:
            tool = InventoryToolImpl(api_client)
            return await tool.execute(
                page_size=page_size,
                current_item=current_item,
                order_by=order_by,
                last_modified_from=last_modified_from,
                branch_ids=branch_ids
            )

    @mcp.tool()
    @with_logging
    @with_error_handling
    @with_validation(InvoicesParams)
    async def get_invoices(
        page_size: Annotated[int, Field(description="Số lượng mục trên mỗi trang (1-100)", ge=1, le=100)] = 50,
        current_item: Annotated[int, Field(description="Chỉ số mục bắt đầu cho phân trang", ge=0)] = 0,
        order_direction: Annotated[str, Field(description="Thứ tự sắp xếp: Asc hoặc Desc")] = "Asc",
        branch_ids: Annotated[Optional[List[int]], Field(description="Danh sách ID chi nhánh để lọc theo")] = None,
        customer_ids: Annotated[Optional[List[int]], Field(description="Danh sách ID khách hàng để lọc theo")] = None,
        customer_code: Annotated[Optional[str], Field(description="Mã khách hàng để lọc theo")] = None,
        status: Annotated[Optional[List[int]], Field(description="Danh sách tình trạng hóa đơn để lọc theo")] = None,
        include_payment: Annotated[Optional[bool], Field(description="Có lấy thông tin thanh toán hay không")] = False,
        include_invoice_delivery: Annotated[Optional[bool], Field(description="Có lấy thông tin giao hàng hay không")] = False,
        last_modified_from: Annotated[Optional[str], Field(description="Thời gian cập nhật từ (định dạng YYYY-MM-DD hoặc ISO datetime)")] = None,
        to_date: Annotated[Optional[str], Field(description="Thời gian cập nhật đến (định dạng YYYY-MM-DD hoặc ISO datetime)")] = None,
        created_date: Annotated[Optional[str], Field(description="Thời gian tạo (định dạng YYYY-MM-DD hoặc ISO datetime)")] = None,
        from_purchase_date: Annotated[Optional[str], Field(description="Từ ngày giao dịch (định dạng YYYY-MM-DD)")] = None,
        to_purchase_date: Annotated[Optional[str], Field(description="Đến ngày giao dịch (định dạng YYYY-MM-DD)")] = None,
        order_id: Annotated[Optional[int], Field(description="Lọc danh sách hóa đơn theo ID của đơn đặt hàng")] = None,
        order_by: Annotated[Optional[str], Field(description="Sắp xếp dữ liệu theo trường (ví dụ: 'createdDate', 'total')")] = None
    ) -> Dict[str, Any]:
        """Lấy danh sách hóa đơn từ KiotViet API với các tùy chọn lọc theo API chính thức.
        
        Công cụ này lấy danh sách hóa đơn từ KiotViet API với hỗ trợ đầy đủ
        các tùy chọn lọc theo API chính thức bao gồm lọc theo chi nhánh, khách hàng,
        trạng thái, thời gian và nhiều tùy chọn khác.
        
        Args:
            page_size: Số lượng mục trên mỗi trang (mặc định: 50, tối đa: 100)
            current_item: Chỉ số mục bắt đầu cho phân trang (mặc định: 0)
            order_direction: Thứ tự sắp xếp - "Asc" hoặc "Desc" (mặc định: "Asc")
            branch_ids: Danh sách ID chi nhánh để lọc theo
            customer_ids: Danh sách ID khách hàng để lọc theo
            customer_code: Mã khách hàng để lọc theo
            status: Danh sách tình trạng hóa đơn để lọc theo
            include_payment: Có lấy thông tin thanh toán hay không (mặc định: False)
            include_invoice_delivery: Có lấy thông tin giao hàng hay không (mặc định: False)
            last_modified_from: Thời gian cập nhật từ (định dạng YYYY-MM-DD hoặc ISO datetime)
            to_date: Thời gian cập nhật đến (định dạng YYYY-MM-DD hoặc ISO datetime)
            created_date: Thời gian tạo (định dạng YYYY-MM-DD hoặc ISO datetime)
            from_purchase_date: Từ ngày giao dịch (định dạng YYYY-MM-DD)
            to_purchase_date: Đến ngày giao dịch (định dạng YYYY-MM-DD)
            order_id: Lọc danh sách hóa đơn theo ID của đơn đặt hàng
            order_by: Sắp xếp dữ liệu theo trường (ví dụ: 'createdDate', 'total')
        
        Returns:
            Dictionary chứa dữ liệu hóa đơn từ API
        """
        api_client = get_api_client()
        async with api_client:
            tool = InvoicesToolImpl(api_client)
            return await tool.execute(
                page_size=page_size,
                current_item=current_item,
                order_direction=order_direction,
                branch_ids=branch_ids,
                customer_ids=customer_ids,
                customer_code=customer_code,
                status=status,
                include_payment=include_payment,
                include_invoice_delivery=include_invoice_delivery,
                last_modified_from=last_modified_from,
                to_date=to_date,
                created_date=created_date,
                from_purchase_date=from_purchase_date,
                to_purchase_date=to_purchase_date,
                order_id=order_id,
                order_by=order_by
            )

    @mcp.tool()
    @with_logging
    @with_error_handling
    @with_validation(RevenueParams)
    async def calculate_daily_revenue(
        from_date: Annotated[Union[str, datetime], Field(description="Start date (YYYY-MM-DD or ISO format)")],
        to_date: Annotated[Union[str, datetime], Field(description="End date (YYYY-MM-DD or ISO format)")],
        include_details: Annotated[bool, Field(description="Include detailed invoice breakdown")] = False
    ) -> Dict[str, Any]:
        """Calculate revenue metrics for a specific date range from KiotViet invoices.

        This tool calculates comprehensive revenue metrics by fetching all invoices in the
        specified date range and computing totals, averages, and optionally detailed breakdowns.

        Args:
            from_date: Start date (datetime object or ISO string like '2025-01-15')
            to_date: End date (datetime object or ISO string like '2025-01-15')
            include_details: Whether to include detailed invoice breakdown (default: False)

        Returns:
            Dictionary containing revenue metrics and summary data
        """
        api_client = get_api_client()
        async with api_client:
            tool = DailyRevenueToolImpl(api_client)
            return await tool.execute(
                from_date=from_date,
                to_date=to_date,
                include_details=include_details
            )

    @mcp.tool()
    @with_logging
    @with_error_handling
    @with_validation(ProductByCodeParams)
    async def get_product_by_code(
        code: Annotated[str, Field(description="Product code to search for", min_length=1, max_length=50)]
    ) -> Dict[str, Any]:
        """Get detailed product information by product code from KiotViet API.

        This tool retrieves comprehensive product details including name, category,
        pricing, and other attributes for a specific product identified by its code.

        Args:
            code: Product code to search for (e.g., "SP001", "PRODUCT-123")

        Returns:
            Dictionary containing detailed product information or error if not found
        """
        api_client = get_api_client()
        async with api_client:
            tool = ProductByCodeToolImpl(api_client)
            return await tool.execute(code=code)

    @mcp.tool()
    @with_logging
    @with_error_handling
    @with_validation(CustomersParams)
    async def get_customers(
        page_size: Annotated[int, Field(description="Số lượng mục trên mỗi trang (1-100)", ge=1, le=100)] = 50,
        current_item: Annotated[int, Field(description="Chỉ số mục bắt đầu cho phân trang", ge=0)] = 0,
        order_direction: Annotated[str, Field(description="Thứ tự sắp xếp: Asc hoặc Desc")] = "Asc",
        code: Annotated[Optional[str], Field(description="Tìm kiếm khách hàng theo mã khách hàng")] = None,
        name: Annotated[Optional[str], Field(description="Tìm kiếm theo tên khách hàng")] = None,
        contact_number: Annotated[Optional[str], Field(description="Tìm kiếm theo số điện thoại khách hàng")] = None,
        last_modified_from: Annotated[Optional[str], Field(description="Thời gian cập nhật (định dạng YYYY-MM-DD hoặc ISO datetime)")] = None,
        order_by: Annotated[Optional[str], Field(description="Sắp xếp dữ liệu theo trường (ví dụ: 'name', 'createdDate')")] = None,
        include_remove_ids: Annotated[Optional[bool], Field(description="Có lấy thông tin danh sách ID bị xóa dựa trên lastModifiedFrom")] = False,
        include_total: Annotated[Optional[bool], Field(description="Có lấy thông tin TotalInvoice, TotalPoint, TotalRevenue")] = False,
        include_customer_group: Annotated[Optional[bool], Field(description="Có lấy thông tin nhóm khách hàng hay không")] = False,
        include_customer_social: Annotated[Optional[bool], Field(description="Có lấy thông tin Psid facebook fanpage của khách hàng hay không")] = False,
        birth_date: Annotated[Optional[str], Field(description="Lọc khách hàng theo ngày sinh nhật (định dạng YYYY-MM-DD)")] = None,
        group_id: Annotated[Optional[int], Field(description="Lọc theo nhóm khách hàng")] = None
    ) -> Dict[str, Any]:
        """Lấy danh sách khách hàng từ KiotViet API với các tùy chọn lọc theo API chính thức.
        
        Công cụ này lấy danh sách khách hàng từ KiotViet API với hỗ trợ đầy đủ
        các tùy chọn tìm kiếm và lọc theo API chính thức bao gồm tìm kiếm theo mã,
        tên, số điện thoại, ngày sinh nhật, nhóm khách hàng và nhiều tùy chọn khác.
        
        Args:
            page_size: Số lượng mục trên mỗi trang (mặc định: 50, tối đa: 100)
            current_item: Chỉ số mục bắt đầu cho phân trang (mặc định: 0)
            order_direction: Thứ tự sắp xếp - "Asc" hoặc "Desc" (mặc định: "Asc")
            code: Tìm kiếm khách hàng theo mã khách hàng
            name: Tìm kiếm theo tên khách hàng
            contact_number: Tìm kiếm theo số điện thoại khách hàng
            last_modified_from: Thời gian cập nhật (định dạng YYYY-MM-DD hoặc ISO datetime)
            order_by: Sắp xếp dữ liệu theo trường (ví dụ: 'name', 'createdDate')
            include_remove_ids: Có lấy thông tin danh sách ID bị xóa dựa trên lastModifiedFrom (mặc định: False)
            include_total: Có lấy thông tin TotalInvoice, TotalPoint, TotalRevenue (mặc định: False)
            include_customer_group: Có lấy thông tin nhóm khách hàng hay không (mặc định: False)
            include_customer_social: Có lấy thông tin Psid facebook fanpage của khách hàng hay không (mặc định: False)
            birth_date: Lọc khách hàng theo ngày sinh nhật (định dạng YYYY-MM-DD)
            group_id: Lọc theo nhóm khách hàng
        
        Returns:
            Dictionary chứa dữ liệu khách hàng từ API
        """
        api_client = get_api_client()
        async with api_client:
            tool = CustomersToolImpl(api_client)
            return await tool.execute(
                page_size=page_size,
                current_item=current_item,
                order_direction=order_direction,
                code=code,
                name=name,
                contact_number=contact_number,
                last_modified_from=last_modified_from,
                order_by=order_by,
                include_remove_ids=include_remove_ids,
                include_total=include_total,
                include_customer_group=include_customer_group,
                include_customer_social=include_customer_social,
                birth_date=birth_date,
                group_id=group_id
            )

    @mcp.tool()
    @with_logging
    @with_error_handling
    @with_validation(PurchaseOrdersParams)
    async def get_purchase_orders(
        page_size: Annotated[int, Field(description="Số lượng mục trên mỗi trang (1-100)", ge=1, le=100)] = 20,
        current_item: Annotated[int, Field(description="Chỉ số mục bắt đầu cho phân trang", ge=0)] = 0,
        order_direction: Annotated[str, Field(description="Thứ tự sắp xếp: Asc hoặc Desc")] = "Asc",
        branchIds: Annotated[Optional[List[int]], Field(description="ID chi nhánh (danh sách)")] = None,
        status: Annotated[Optional[List[int]], Field(description="Tình trạng đặt hàng (danh sách)")] = None,
        includePayment: Annotated[Optional[bool], Field(description="Có lấy thông tin thanh toán")] = False,
        includeOrderDelivery: Annotated[Optional[bool], Field(description="Có lấy thông tin giao hàng")] = False,
        fromPurchaseDate: Annotated[Optional[str], Field(description="Từ ngày nhập hàng (YYYY-MM-DD)")] = None,
        toPurchaseDate: Annotated[Optional[str], Field(description="Đến ngày nhập hàng (YYYY-MM-DD)")] = None
    ) -> Dict[str, Any]:
        """Lấy danh sách đơn nhập hàng từ KiotViet API với các tùy chọn lọc toàn diện.
        
        Công cụ này lấy danh sách đơn nhập hàng từ KiotViet API với hỗ trợ nhiều
        tùy chọn lọc bao gồm phạm vi ngày, lọc chi nhánh, lọc nhà cung cấp,
        lọc trạng thái và nhiều hơn nữa. Hỗ trợ phân trang và sắp xếp.
        
        Args:
            page_size: Số lượng mục trên mỗi trang (mặc định: 50, tối đa: 100)
            current_item: Chỉ số mục bắt đầu cho phân trang (mặc định: 0)
            order_direction: Thứ tự sắp xếp - "Asc" hoặc "Desc" (mặc định: "Asc")
            from_purchase_date: Ngày bắt đầu cho bộ lọc ngày nhập hàng (định dạng YYYY-MM-DD)
            to_purchase_date: Ngày kết thúc cho bộ lọc ngày nhập hàng (định dạng YYYY-MM-DD)
            from_created_date: Ngày bắt đầu cho bộ lọc ngày tạo (định dạng YYYY-MM-DD)
            to_created_date: Ngày kết thúc cho bộ lọc ngày tạo (định dạng YYYY-MM-DD)
            from_modified_date: Ngày bắt đầu cho bộ lọc ngày sửa đổi (định dạng YYYY-MM-DD)
            to_modified_date: Ngày kết thúc cho bộ lọc ngày sửa đổi (định dạng YYYY-MM-DD)
            ids: Danh sách ID đơn nhập hàng cụ thể cần lấy
            branch_ids: Danh sách ID chi nhánh để lọc theo
            supplier_ids: Danh sách ID nhà cung cấp để lọc theo
            status: Lọc theo trạng thái đơn nhập hàng (1=Chờ duyệt, 2=Đã duyệt, 3=Đã hủy, v.v.)
            order_by: Trường để sắp xếp theo (ví dụ: 'purchaseDate', 'total')
            include_purchase_order_details: Bao gồm chi tiết dòng đơn nhập hàng (mặc định: False)
        
        Returns:
            Dictionary chứa dữ liệu đơn nhập hàng từ API
        """
        api_client = get_api_client()
        async with api_client:
            tool = PurchaseOrdersToolImpl(api_client)
            return await tool.execute(
                page_size=page_size,
                current_item=current_item,
                order_direction=order_direction,
                # Tham số chuẩn theo API spec
                branchIds=branchIds,
                status=status,
                includePayment=includePayment,
                includeOrderDelivery=includeOrderDelivery,
                fromPurchaseDate=fromPurchaseDate,
                toPurchaseDate=toPurchaseDate
            )

    @mcp.tool()
    @with_logging
    @with_error_handling
    @with_validation(ReturnsParams)
    async def get_returns(
        page_size: Annotated[int, Field(description="Số lượng mục trên mỗi trang (1-100)", ge=1, le=100)] = 20,
        current_item: Annotated[int, Field(description="Chỉ số mục bắt đầu cho phân trang", ge=0)] = 0,
        order_direction: Annotated[str, Field(description="Thứ tự sắp xếp: Asc hoặc Desc")] = "Asc",
        orderBy: Annotated[Optional[str], Field(description="Sắp xếp dữ liệu theo trường orderBy (ví dụ: orderBy=Name)")] = None,
        lastModifiedFrom: Annotated[Optional[str], Field(description="Thời gian cập nhật (định dạng datetime)")] = None,
        fromReturnDate: Annotated[Optional[str], Field(description="Từ ngày trả hàng (YYYY-MM-DD)")] = None,
        toReturnDate: Annotated[Optional[str], Field(description="Đến ngày trả hàng (YYYY-MM-DD)")] = None,
        includePayment: Annotated[Optional[bool], Field(description="Có lấy thông tin danh sách thanh toán")] = False
    ) -> Dict[str, Any]:
        """Lấy danh sách đơn trả hàng từ KiotViet API với các tùy chọn lọc toàn diện.
        
        Công cụ này lấy danh sách đơn trả hàng từ KiotViet API với hỗ trợ nhiều
        tùy chọn lọc bao gồm phạm vi ngày, lọc chi nhánh, lọc khách hàng,
        lọc hóa đơn gốc, lọc trạng thái và nhiều hơn nữa. Hỗ trợ phân trang và sắp xếp.
        
        Args:
            page_size: Số lượng mục trên mỗi trang (mặc định: 50, tối đa: 100)
            current_item: Chỉ số mục bắt đầu cho phân trang (mặc định: 0)
            order_direction: Thứ tự sắp xếp - "Asc" hoặc "Desc" (mặc định: "Asc")
            from_return_date: Ngày bắt đầu cho bộ lọc ngày trả hàng (định dạng YYYY-MM-DD)
            to_return_date: Ngày kết thúc cho bộ lọc ngày trả hàng (định dạng YYYY-MM-DD)
            from_created_date: Ngày bắt đầu cho bộ lọc ngày tạo (định dạng YYYY-MM-DD)
            to_created_date: Ngày kết thúc cho bộ lọc ngày tạo (định dạng YYYY-MM-DD)
            from_modified_date: Ngày bắt đầu cho bộ lọc ngày sửa đổi (định dạng YYYY-MM-DD)
            to_modified_date: Ngày kết thúc cho bộ lọc ngày sửa đổi (định dạng YYYY-MM-DD)
            ids: Danh sách ID đơn trả hàng cụ thể cần lấy
            branch_ids: Danh sách ID chi nhánh để lọc theo
            customer_ids: Danh sách ID khách hàng để lọc theo
            invoice_ids: Danh sách ID hóa đơn gốc để lọc theo
            status: Lọc theo trạng thái đơn trả hàng (1=Chờ duyệt, 2=Đã duyệt, 3=Đã hủy, v.v.)
            order_by: Trường để sắp xếp theo (ví dụ: 'returnDate', 'total')
            include_return_details: Bao gồm chi tiết dòng đơn trả hàng (mặc định: False)
        
        Returns:
            Dictionary chứa dữ liệu đơn trả hàng từ API
        """
        api_client = get_api_client()
        async with api_client:
            tool = ReturnsToolImpl(api_client)
            return await tool.execute(
                page_size=page_size,
                current_item=current_item,
                order_direction=order_direction,
                # Tham số chuẩn theo API spec
                orderBy=orderBy,
                lastModifiedFrom=lastModifiedFrom,
                fromReturnDate=fromReturnDate,
                toReturnDate=toReturnDate,
                includePayment=includePayment
            )

    @mcp.tool()
    @with_logging
    @with_error_handling
    @with_validation(CashFlowParams)
    async def get_cashflow(
        page_size: Annotated[int, Field(description="Số lượng mục trên mỗi trang (1-100)", ge=1, le=100)] = 20,
        current_item: Annotated[int, Field(description="Chỉ số mục bắt đầu cho phân trang", ge=0)] = 0,
        order_direction: Annotated[str, Field(description="Thứ tự sắp xếp: Asc hoặc Desc")] = "Asc",
        branchIds: Annotated[Optional[List[int]], Field(description="ID chi nhánh")] = None,
        code: Annotated[Optional[List[str]], Field(description="Danh sách mã code của phiếu")] = None,
        userId: Annotated[Optional[int], Field(description="Id người tạo")] = None,
        accountId: Annotated[Optional[int], Field(description="Tài khoản nhận")] = None,
        partnerType: Annotated[Optional[str], Field(description="Loại người nộp/nhận: A: tất cả, C: khách hàng, S: nhà cung cấp, U: nhân viên, D: tối tác giao hàng, O: khác")] = None,
        method: Annotated[Optional[List[str]], Field(description="Danh sách phương thức thanh toán")] = None,
        cashFlowGroupId: Annotated[Optional[List[int]], Field(description="Loại thu/chi")] = None,
        usedForFinancialReporting: Annotated[Optional[int], Field(description="Lọc theo kết qua kinh doanh: 0: không hoạch toán, 1: đưa vào hoạch toán")] = None,
        partnerName: Annotated[Optional[str], Field(description="Tên người nộp/nhận")] = None,
        contactNumber: Annotated[Optional[str], Field(description="Số điện thoại người nộp/nhận")] = None,
        isReceipt: Annotated[Optional[bool], Field(description="Theo phiếu thu/chi; True: thu, false: chi")] = None,
        includeAccount: Annotated[Optional[bool], Field(description="Lấy thông tin tài khoản ngân hàng hay không")] = False,
        includeBranch: Annotated[Optional[bool], Field(description="Lấy tên chi nhánh hay không")] = False,
        includeUser: Annotated[Optional[bool], Field(description="Lấy tên người tạo hay không")] = False,
        startDate: Annotated[Optional[str], Field(description="Thời gian bắt đầu")] = None,
        endDate: Annotated[Optional[str], Field(description="Thời gian kết thúc")] = None,
        status: Annotated[Optional[int], Field(description="Trạng thái phiếu; 0: Đã thanh toán, 1: Đã hủy, không truyền: tất cả")] = None,
        ids: Annotated[Optional[List[int]], Field(description="Id phiếu thu/chi")] = None
    ) -> Dict[str, Any]:
        """Lấy danh sách phiếu thu chi từ KiotViet API với các tùy chọn lọc toàn diện theo API chính thức.
        
        Công cụ này lấy danh sách phiếu thu chi trong sổ quỹ từ KiotViet API với hỗ trợ đầy đủ
        các tùy chọn lọc theo API chính thức bao gồm lọc theo chi nhánh, mã phiếu, người tạo,
        loại đối tác, phương thức thanh toán, trạng thái và nhiều hơn nữa.
        
        Args:
            page_size: Số lượng mục trên mỗi trang (mặc định: 50, tối đa: 100)
            current_item: Chỉ số mục bắt đầu cho phân trang (mặc định: 0)
            order_direction: Thứ tự sắp xếp - "Asc" hoặc "Desc" (mặc định: "Asc")
            branch_ids: Danh sách ID chi nhánh để lọc theo
            codes: Danh sách mã code của phiếu thu chi
            user_id: ID người tạo phiếu
            account_id: ID tài khoản nhận
            partner_type: Loại người nộp/nhận (A=tất cả, C=khách hàng, S=nhà cung cấp, U=nhân viên, D=đối tác giao hàng, O=khác)
            methods: Danh sách phương thức thanh toán
            cashflow_group_ids: Danh sách ID loại thu/chi
            used_for_financial_reporting: Lọc theo kết quả kinh doanh (0=không hoạch toán, 1=đưa vào hoạch toán)
            partner_name: Tên người nộp/nhận
            contact_number: Số điện thoại người nộp/nhận
            is_receipt: Lọc theo phiếu thu/chi (True=thu, False=chi)
            include_account: Lấy thông tin tài khoản ngân hàng hay không (mặc định: False)
            include_branch: Lấy tên chi nhánh hay không (mặc định: False)
            include_user: Lấy tên người tạo hay không (mặc định: False)
            start_date: Thời gian bắt đầu (định dạng YYYY-MM-DD)
            end_date: Thời gian kết thúc (định dạng YYYY-MM-DD)
            status: Trạng thái phiếu (0=Đã thanh toán, 1=Đã hủy, không truyền=tất cả)
            ids: Danh sách ID phiếu thu chi cụ thể cần lấy
            order_by: Trường để sắp xếp theo
        
        Returns:
            Dictionary chứa dữ liệu phiếu thu chi từ API
        """
        api_client = get_api_client()
        async with api_client:
            tool = CashFlowToolImpl(api_client)
            return await tool.execute(
                page_size=page_size,
                current_item=current_item,
                order_direction=order_direction,
                branchIds=branchIds,
                code=code,
                userId=userId,
                accountId=accountId,
                partnerType=partnerType,
                method=method,
                cashFlowGroupId=cashFlowGroupId,
                usedForFinancialReporting=usedForFinancialReporting,
                partnerName=partnerName,
                contactNumber=contactNumber,
                isReceipt=isReceipt,
                includeAccount=includeAccount,
                includeBranch=includeBranch,
                includeUser=includeUser,
                startDate=startDate,
                endDate=endDate,
                status=status,
                ids=ids
            )
            
    @mcp.tool()
    @with_logging
    @with_error_handling
    @with_validation(OrdersParams)
    async def get_orders(
        page_size: Annotated[int, Field(description="Số lượng mục trên mỗi trang (1-100)", ge=1, le=100)] = 50,
        current_item: Annotated[int, Field(description="Chỉ số mục bắt đầu cho phân trang", ge=0)] = 0,
        order_direction: Annotated[str, Field(description="Thứ tự sắp xếp: Asc hoặc Desc")] = "Asc",
        branch_ids: Annotated[Optional[List[int]], Field(description="Danh sách ID chi nhánh để lọc theo")] = None,
        customer_ids: Annotated[Optional[List[int]], Field(description="Danh sách ID khách hàng để lọc theo")] = None,
        customer_code: Annotated[Optional[str], Field(description="Mã khách hàng để lọc theo")] = None,
        status: Annotated[Optional[List[int]], Field(description="Danh sách trạng thái đơn đặt hàng để lọc theo (1: Chờ xử lý, 2: Đang xử lý, 3: Hoàn thành, 4: Hủy)")] = None,
        sale_channel_id: Annotated[Optional[int], Field(description="ID kênh bán hàng để lọc theo")] = None,
        include_payment: Annotated[Optional[bool], Field(description="Có bao gồm thông tin thanh toán hay không")] = False,
        include_order_delivery: Annotated[Optional[bool], Field(description="Có bao gồm thông tin giao hàng hay không")] = False,
        last_modified_from: Annotated[Optional[str], Field(description="Thời gian cập nhật từ (định dạng YYYY-MM-DD hoặc ISO datetime)")] = None,
        to_date: Annotated[Optional[str], Field(description="Thời gian cập nhật đến (định dạng YYYY-MM-DD hoặc ISO datetime)")] = None,
        created_date: Annotated[Optional[str], Field(description="Ngày tạo đơn hàng (định dạng YYYY-MM-DD)")] = None,
        order_by: Annotated[Optional[str], Field(description="Trường để sắp xếp theo (ví dụ: 'purchaseDate', 'total')")] = None
    ) -> Dict[str, Any]:
        """Lấy danh sách đơn đặt hàng từ KiotViet API với các tùy chọn lọc toàn diện theo API chính thức.
        
        Công cụ này lấy danh sách đơn đặt hàng từ KiotViet API với hỗ trợ đầy đủ
        các tùy chọn lọc theo API chính thức bao gồm lọc theo chi nhánh, khách hàng,
        trạng thái, kênh bán hàng và nhiều hơn nữa.
        
        Args:
            page_size: Số lượng mục trên mỗi trang (mặc định: 50, tối đa: 100)
            current_item: Chỉ số mục bắt đầu cho phân trang (mặc định: 0)
            order_direction: Thứ tự sắp xếp - "Asc" hoặc "Desc" (mặc định: "Asc")
            branch_ids: Danh sách ID chi nhánh để lọc theo
            customer_ids: Danh sách ID khách hàng để lọc theo
            customer_code: Mã khách hàng để lọc theo
            status: Danh sách trạng thái đơn đặt hàng để lọc theo (1: Chờ xử lý, 2: Đang xử lý, 3: Hoàn thành, 4: Hủy)
            sale_channel_id: ID kênh bán hàng để lọc theo
            include_payment: Có bao gồm thông tin thanh toán hay không (mặc định: False)
            include_order_delivery: Có bao gồm thông tin giao hàng hay không (mặc định: False)
            last_modified_from: Thời gian cập nhật từ (định dạng YYYY-MM-DD hoặc ISO datetime)
            to_date: Thời gian cập nhật đến (định dạng YYYY-MM-DD hoặc ISO datetime)
            created_date: Ngày tạo đơn hàng (định dạng YYYY-MM-DD)
            order_by: Trường để sắp xếp theo (ví dụ: 'purchaseDate', 'total')
        
        Returns:
            Dictionary chứa dữ liệu đơn đặt hàng từ API
        """
        api_client = get_api_client()
        async with api_client:
            tool = OrdersToolImpl(api_client)
            return await tool.execute(
                page_size=page_size,
                current_item=current_item,
                order_direction=order_direction,
                branch_ids=branch_ids,
                customer_ids=customer_ids,
                customer_code=customer_code,
                status=status,
                sale_channel_id=sale_channel_id,
                include_payment=include_payment,
                include_order_delivery=include_order_delivery,
                last_modified_from=last_modified_from,
                to_date=to_date,
                created_date=created_date,
                order_by=order_by
            )