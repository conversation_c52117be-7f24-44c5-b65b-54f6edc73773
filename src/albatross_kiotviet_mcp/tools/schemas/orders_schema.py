"""Parameter validation schemas for orders tool."""

from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, Field, field_validator, validator
from .common_schemas import PaginationParams, OrderDirectionEnum


class OrdersParams(PaginationParams):
    """Parameters for get_orders tool with comprehensive filtering options."""
    
    # Filtering parameters
    branch_ids: Optional[List[int]] = Field(
        None, 
        description="Danh sách ID chi nhánh để lọc đơn đặt hàng"
    )
    
    customer_ids: Optional[List[int]] = Field(
        None,
        description="Danh sách ID khách hàng để lọc đơn đặt hàng"
    )
    
    customer_code: Optional[str] = Field(
        None,
        description="Mã khách hàng để tìm kiếm đơn đặt hàng"
    )
    
    status: Optional[List[int]] = Field(
        None,
        description="Danh sách trạng thái đơn đặt hàng (1: Ch<PERSON> xử lý, 2: <PERSON><PERSON> xử lý, 3: <PERSON><PERSON><PERSON> thành, 4: Hủ<PERSON>)"
    )
    
    sale_channel_id: Optional[int] = Field(
        None,
        description="ID kênh bán hàng, nếu không truyền mặc định kênh khác"
    )
    
    # Include options
    include_payment: Optional[bool] = Field(
        None,
        description="Có bao gồm thông tin thanh toán trong response hay không"
    )
    
    include_order_delivery: Optional[bool] = Field(
        None,
        description="Có bao gồm thông tin giao hàng trong response hay không"
    )
    
    # Date filtering
    last_modified_from: Optional[str] = Field(
        None,
        description="Lọc đơn đặt hàng được cập nhật từ thời điểm này (định dạng: YYYY-MM-DD hoặc YYYY-MM-DD HH:MM:SS)"
    )
    
    to_date: Optional[str] = Field(
        None,
        description="Lọc đơn đặt hàng được cập nhật cho đến thời điểm này (định dạng: YYYY-MM-DD hoặc YYYY-MM-DD HH:MM:SS)"
    )
    
    created_date: Optional[str] = Field(
        None,
        description="Lọc đơn đặt hàng được tạo vào ngày này (định dạng: YYYY-MM-DD)"
    )
    
    # Sorting
    order_by: Optional[str] = Field(
        None,
        description="Trường để sắp xếp kết quả (ví dụ: id, code, purchaseDate, total, createdDate)"
    )
    
    order_direction: OrderDirectionEnum = Field(
        OrderDirectionEnum.ASC,
        description="Hướng sắp xếp: Asc (tăng dần) hoặc Desc (giảm dần)"
    )
    
    @field_validator('status')
    def validate_status(cls, v):
        """Validate order status values."""
        if v is not None:
            valid_statuses = [1, 2, 3, 4]  # Chờ xử lý, Đang xử lý, Hoàn thành, Hủy
            for status in v:
                if status not in valid_statuses:
                    raise ValueError(f"Invalid status: {status}. Valid values are: {valid_statuses}")
        return v
    
    @field_validator('branch_ids')
    def validate_branch_ids(cls, v):
        """Validate branch IDs are positive integers."""
        if v is not None:
            for branch_id in v:
                if branch_id <= 0:
                    raise ValueError(f"Branch ID must be positive: {branch_id}")
        return v
    
    @field_validator('customer_ids')
    def validate_customer_ids(cls, v):
        """Validate customer IDs are positive integers."""
        if v is not None:
            for customer_id in v:
                if customer_id <= 0:
                    raise ValueError(f"Customer ID must be positive: {customer_id}")
        return v
    
    @field_validator('sale_channel_id')
    def validate_sale_channel_id(cls, v):
        """Validate sale channel ID is positive."""
        if v is not None and v <= 0:
            raise ValueError("Sale channel ID must be positive")
        return v