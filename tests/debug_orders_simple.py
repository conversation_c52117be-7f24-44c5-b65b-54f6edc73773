#!/usr/bin/env python3
"""
Simple debug script for testing get_orders tool with KiotViet API.
"""

import sys
import os
import asyncio
from datetime import datetime, timedelta

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

from albatross_kiotviet_mcp.tools.get_orders_tool_impl import OrdersToolImpl


async def test_orders_tool():
    """Test the orders tool with various scenarios."""
    print("🧪 Testing Orders Tool Implementation")
    print("=" * 50)
    
    tool = OrdersToolImpl()
    
    try:
        # Test 1: Basic orders retrieval
        print("\n📋 Test 1: Basic orders retrieval")
        result = await tool.execute(page_size=5)
        
        print(f"✅ Success: Retrieved {len(result['data'])} orders")
        print(f"📊 Total orders: {result['total']}")
        
        if result['data']:
            first_order = result['data'][0]
            print(f"📄 First order: {first_order['code']} - {first_order.get('customerName', 'N/A')}")
            print(f"💰 Total: {first_order.get('total', 0):,.0f} VND")
            print(f"📅 Date: {first_order.get('purchaseDate', 'N/A')}")
            print(f"🏪 Branch: {first_order.get('branchName', 'N/A')}")
            print(f"📊 Status: {first_order.get('statusValue', 'N/A')}")
        
        # Test 2: Orders with status filter
        print("\n📋 Test 2: Orders with status filter (Completed)")
        result_completed = await tool.execute(
            page_size=3,
            status=[3],  # Hoàn thành
            include_payment=True
        )
        
        print(f"✅ Success: Retrieved {len(result_completed['data'])} completed orders")
        
        if result_completed['data']:
            for order in result_completed['data']:
                print(f"  📄 {order['code']} - Status: {order.get('statusValue', 'N/A')}")
                if order.get('payments'):
                    print(f"    💳 Payments: {len(order['payments'])} payment(s)")
        
        # Test 3: Orders with date filter (last 30 days)
        print("\n📋 Test 3: Orders from last 30 days")
        thirty_days_ago = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
        
        result_recent = await tool.execute(
            page_size=3,
            last_modified_from=thirty_days_ago,
            order_by="createdDate",
            order_direction="Desc"
        )
        
        print(f"✅ Success: Retrieved {len(result_recent['data'])} recent orders")
        print(f"📅 Date filter: From {thirty_days_ago}")
        
        # Test 4: Orders with customer filter (if we have customer data)
        if result['data'] and result['data'][0].get('customerId'):
            print("\n📋 Test 4: Orders by specific customer")
            customer_id = result['data'][0]['customerId']
            
            result_customer = await tool.execute(
                page_size=5,
                customer_ids=[customer_id],
                include_payment=True,
                include_order_delivery=True
            )
            
            print(f"✅ Success: Retrieved {len(result_customer['data'])} orders for customer {customer_id}")
            
            if result_customer['data']:
                customer_name = result_customer['data'][0].get('customerName', 'N/A')
                print(f"👤 Customer: {customer_name}")
                total_amount = sum(order.get('total', 0) for order in result_customer['data'])
                print(f"💰 Total amount: {total_amount:,.0f} VND")
        
        # Test 5: Error handling - invalid status
        print("\n📋 Test 5: Error handling (invalid status)")
        try:
            await tool.execute(status=[999])  # Invalid status
            print("❌ Should have raised ValueError")
        except ValueError as e:
            print(f"✅ Correctly caught error: {e}")
        
        print("\n🎉 All tests completed successfully!")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True


async def main():
    """Main test function."""
    print("🚀 Starting Orders Tool Debug Test")
    print(f"⏰ Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    success = await test_orders_tool()
    
    if success:
        print("\n✅ All tests passed!")
        return 0
    else:
        print("\n❌ Some tests failed!")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())